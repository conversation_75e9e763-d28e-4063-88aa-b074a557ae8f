// Add ImGui includes and DirectX 11 includes
#include "imgui/imgui.h"
#include "imgui/imgui_impl_win32.h"
#include "imgui/imgui_impl_dx11.h"
#include <d3d11.h>
#include <tchar.h>
#include <windows.h>
#include <chrono>
#include <thread>
#include <vector>
#include <algorithm>
#include <string>

// Link necessary libraries
#pragma comment(lib, "d3d11.lib")

// Structure to store captured color information
struct CapturedColor {
    int r, g, b;
    int count; // How many times this color was found
};

// Triggerbot configuration structure
struct TriggerbotConfig {
    bool enabled = false;
    bool wasEnabled = false; // Track previous state to detect toggle
    int activationKey = VK_LBUTTON; // Left mouse button by default
    int pixelBoxSize = 5; // Size of the detection box around crosshair
    int reactionDelay = 50; // Delay in milliseconds before shooting
    int shootTime = 100; // How long to hold the shoot button in milliseconds
    int shootMode = 0; // 0 = Single shot, 1 = Burst, 2 = Auto
    float colorTolerance = 0.1f; // Color matching tolerance
    bool showStatus = true; // Show status information
    bool showCrosshair = false; // Show detection area overlay
    int burstCount = 3; // Number of shots in burst mode
    int burstDelay = 100; // Delay between burst shots
    bool autoCapture = false; // Don't auto-capture on enable, capture on key press
    int captureThreshold = 2; // Minimum pixel count to consider a color significant
    int maxColors = 50; // Maximum number of colors to track (increased from 20)
    bool continuousMonitoring = true; // Continuously monitor for color changes
    float changeThreshold = 0.4f; // Threshold for detecting abrupt changes (enemies vs background)
    int monitoringInterval = 50; // Fast monitoring for quick enemy detection
    int sampleRate = 2; // Higher resolution sampling for better enemy detection
    int monitoringMode = 0; // 0 = Only when activation key held, 1 = Constant monitoring
    bool shootOnColorChange = true; // Automatically shoot when color changes are detected
    int shootButton = VK_LBUTTON; // Button to simulate for shooting (left mouse button)
    int stabilityFrames = 2; // Fewer frames needed for abrupt changes
    int brightnessThreshold = 40; // Minimum brightness difference to detect enemies (0-100)
    bool captureOnKeyPress = true; // Capture baseline colors when key is first pressed
    bool keyWasPressed = false; // Track key state for capture timing
};

// Dynamic color tracking
static std::vector<CapturedColor> g_capturedColors;
static std::vector<CapturedColor> g_currentColors; // Current colors in detection box
static bool g_colorsLearned = false;
static bool g_colorChangeDetected = false;
static std::chrono::steady_clock::time_point g_lastColorUpdate;
static std::chrono::steady_clock::time_point g_lastMonitoringUpdate;
static int g_stabilityCounter = 0; // Counter for consecutive change detections
static std::vector<CapturedColor> g_previousColors; // Previous frame colors for stability checking

// Global triggerbot configuration
static TriggerbotConfig g_config;
static bool g_triggerbotActive = false;
static std::chrono::steady_clock::time_point g_lastDetection;
static std::chrono::steady_clock::time_point g_lastShot;

// Global variables for DirectX 11
static ID3D11Device*           g_pd3dDevice = NULL;
static ID3D11DeviceContext*    g_pd3dDeviceContext = NULL;
static IDXGISwapChain*         g_pSwapChain = NULL;
static ID3D11RenderTargetView* g_mainRenderTargetView = NULL;
static HWND                   g_hWnd = NULL;

// Forward declarations
bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Triggerbot utility functions
COLORREF GetPixelColor(int x, int y);
void CaptureColorsInBox();
void MonitorColorsInBox(); // Continuously monitor colors
bool IsTargetColor(COLORREF color);
bool DetectTarget();
bool DetectColorChange(); // Detect if colors have changed significantly
void SimulateMouseClick();
void UpdateTriggerbot();
const char* GetKeyName(int vk);
void ResetCapturedColors();
float CalculateColorSimilarity(const std::vector<CapturedColor>& colors1, const std::vector<CapturedColor>& colors2);
float CalculateAverageBrightness(const std::vector<CapturedColor>& colors);

// New Win32 window creation for ImGui
HWND CreateAppWindow(HINSTANCE hInstance, int nCmdShow) {
    WNDCLASSEX wc = { sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0, 0,
                      GetModuleHandle(NULL), NULL, NULL, NULL, NULL,
                      _T("TriggerbotImGuiClass"), NULL };
    RegisterClassEx(&wc);

    HWND hwnd = CreateWindow(wc.lpszClassName, _T("Triggerbot ImGui Window"),
                             WS_OVERLAPPEDWINDOW, 100, 100, 800, 600,
                             NULL, NULL, wc.hInstance, NULL);

    ShowWindow(hwnd, nCmdShow);
    UpdateWindow(hwnd);

    return hwnd;
}

// Main function with ImGui integration
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE, LPSTR, int nCmdShow) {
    // Create application window
    g_hWnd = CreateAppWindow(hInstance, nCmdShow);
    if (!CreateDeviceD3D(g_hWnd)) {
        CleanupDeviceD3D();
        UnregisterClass(_T("TriggerbotImGuiClass"), hInstance);
        return 1;
    }

    // Setup ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;

    // Setup ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(g_hWnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    // Main loop
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }

        // Start ImGui frame
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Update triggerbot logic
        UpdateTriggerbot();

        // Modern full-window UI
        ImGuiViewport* viewport = ImGui::GetMainViewport();
        ImGui::SetNextWindowPos(viewport->WorkPos);
        ImGui::SetNextWindowSize(viewport->WorkSize);

        ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse |
                                       ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove |
                                       ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoNavFocus;

        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(20.0f, 20.0f));

        ImGui::Begin("Triggerbot Control Panel", nullptr, window_flags);

        // Header section
        ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]); // Use default font but larger
        ImGui::SetWindowFontScale(1.5f);
        ImGui::Text("🎯 TRIGGERBOT CONTROL PANEL");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopFont();

        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        // Main enable/disable toggle with modern styling
        ImGui::PushStyleColor(ImGuiCol_Button, g_config.enabled ? ImVec4(0.2f, 0.7f, 0.2f, 1.0f) : ImVec4(0.7f, 0.2f, 0.2f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, g_config.enabled ? ImVec4(0.3f, 0.8f, 0.3f, 1.0f) : ImVec4(0.8f, 0.3f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, g_config.enabled ? ImVec4(0.1f, 0.6f, 0.1f, 1.0f) : ImVec4(0.6f, 0.1f, 0.1f, 1.0f));

        ImGui::SetWindowFontScale(1.2f);
        if (ImGui::Button(g_config.enabled ? "🟢 TRIGGERBOT ENABLED" : "🔴 TRIGGERBOT DISABLED", ImVec2(300, 50))) {
            g_config.enabled = !g_config.enabled;
        }
        ImGui::SetWindowFontScale(1.0f);

        ImGui::PopStyleColor(3);
        ImGui::Spacing();

        // Create two-column layout
        ImGui::Columns(2, "MainColumns", true);
        ImGui::SetColumnWidth(0, ImGui::GetWindowWidth() * 0.6f);

        // Left Column - Main Controls
        ImGui::BeginChild("LeftPanel", ImVec2(0, 0), true, ImGuiWindowFlags_AlwaysUseWindowPadding);

        // Section 1: Basic Controls
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.3f, 0.8f, 1.0f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("⚙️ BASIC CONTROLS");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        // Activation key selection with modern styling
        ImGui::Text("Activation Key:");
        ImGui::SameLine();
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.4f, 0.8f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.5f, 0.9f, 1.0f));
        if (ImGui::Button(GetKeyName(g_config.activationKey), ImVec2(150, 30))) {
            ImGui::OpenPopup("Key Selection");
        }
        ImGui::PopStyleColor(2);

        if (ImGui::BeginPopup("Key Selection")) {
            ImGui::Text("Select Activation Key:");
            ImGui::Separator();
            if (ImGui::Selectable("🖱️ Left Mouse")) g_config.activationKey = VK_LBUTTON;
            if (ImGui::Selectable("🖱️ Right Mouse")) g_config.activationKey = VK_RBUTTON;
            if (ImGui::Selectable("🖱️ Middle Mouse")) g_config.activationKey = VK_MBUTTON;
            if (ImGui::Selectable("🖱️ Mouse 4")) g_config.activationKey = VK_XBUTTON1;
            if (ImGui::Selectable("🖱️ Mouse 5")) g_config.activationKey = VK_XBUTTON2;
            if (ImGui::Selectable("⌨️ Shift")) g_config.activationKey = VK_SHIFT;
            if (ImGui::Selectable("⌨️ Ctrl")) g_config.activationKey = VK_CONTROL;
            if (ImGui::Selectable("⌨️ Alt")) g_config.activationKey = VK_MENU;
            if (ImGui::Selectable("⌨️ Space")) g_config.activationKey = VK_SPACE;
            ImGui::EndPopup();
        }

        ImGui::Spacing();
        ImGui::Spacing();

        // Section 2: Detection Settings
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.3f, 0.8f, 1.0f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("🎯 DETECTION SETTINGS");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        ImGui::Text("Detection Box Size:");
        ImGui::SliderInt("##boxsize", &g_config.pixelBoxSize, 5, 100);
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Box covers %dx%d pixels", g_config.pixelBoxSize, g_config.pixelBoxSize);

        ImGui::Spacing();
        ImGui::Text("Reaction Delay:");
        ImGui::SliderInt("##delay", &g_config.reactionDelay, 0, 500);
        ImGui::SameLine(); ImGui::Text("ms");

        ImGui::Spacing();
        ImGui::Text("Shoot Duration:");
        ImGui::SliderInt("##shoottime", &g_config.shootTime, 10, 1000);
        ImGui::SameLine(); ImGui::Text("ms");

        ImGui::Spacing();
        ImGui::Spacing();

        // Section 3: Color Detection
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.3f, 0.8f, 1.0f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("🌈 COLOR DETECTION");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        ImGui::Checkbox("Auto-capture colors on enable", &g_config.autoCapture);
        ImGui::Checkbox("Continuous monitoring", &g_config.continuousMonitoring);

        if (g_config.continuousMonitoring) {
            ImGui::Spacing();
            ImGui::Text("Monitoring Mode:");
            const char* monitoringModes[] = { "Only when key held", "Constant monitoring" };
            ImGui::Combo("##monitoring", &g_config.monitoringMode, monitoringModes, 2);
            if (g_config.monitoringMode == 0) {
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "✓ Best performance");
            } else {
                ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "⚠ Higher CPU usage");
            }
        }

        ImGui::Spacing();
        ImGui::Text("Max Colors:");
        ImGui::SliderInt("##maxcolors", &g_config.maxColors, 10, 100);

        ImGui::Text("Capture Threshold:");
        ImGui::SliderInt("##threshold", &g_config.captureThreshold, 1, 10);

        ImGui::Text("Color Tolerance:");
        ImGui::SliderFloat("##tolerance", &g_config.colorTolerance, 0.0f, 1.0f);

        ImGui::Text("Change Threshold:");
        ImGui::SliderFloat("##changethreshold", &g_config.changeThreshold, 0.2f, 0.8f);
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Color pattern sensitivity");

        ImGui::Text("Brightness Threshold:");
        ImGui::SliderInt("##brightness", &g_config.brightnessThreshold, 20, 100);
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Enemy brightness difference");

        ImGui::Text("Stability Frames:");
        ImGui::SliderInt("##stability", &g_config.stabilityFrames, 1, 5);
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Consecutive detections required");

        ImGui::Spacing();
        ImGui::Checkbox("Capture on key press", &g_config.captureOnKeyPress);
        ImGui::Checkbox("Auto-shoot on enemy detection", &g_config.shootOnColorChange);
        if (g_config.shootOnColorChange) {
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "✓ Will shoot when enemies appear");
        }

        ImGui::Spacing();
        ImGui::Spacing();

        // Section 4: Performance Settings
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.3f, 0.8f, 1.0f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("⚡ PERFORMANCE");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        ImGui::Text("Monitor Interval:");
        ImGui::SliderInt("##interval", &g_config.monitoringInterval, 50, 500);
        ImGui::SameLine(); ImGui::Text("ms");

        ImGui::Text("Sample Rate:");
        ImGui::SliderInt("##samplerate", &g_config.sampleRate, 1, 8);
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Check every %d pixel(s)", g_config.sampleRate);

        ImGui::EndChild();

        // Right Column - Color Display and Status
        ImGui::NextColumn();
        ImGui::BeginChild("RightPanel", ImVec2(0, 0), true, ImGuiWindowFlags_AlwaysUseWindowPadding);

        // Section 1: Color Controls
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.6f, 0.2f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("🎨 COLOR CONTROLS");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        // Modern button styling for color controls
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.6f, 0.2f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.7f, 0.3f, 1.0f));
        if (ImGui::Button("📸 Capture Colors", ImVec2(-1, 35))) {
            CaptureColorsInBox();
        }
        ImGui::PopStyleColor(2);

        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.6f, 0.2f, 0.2f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.7f, 0.3f, 0.3f, 1.0f));
        if (ImGui::Button("🗑️ Reset Colors", ImVec2(-1, 35))) {
            ResetCapturedColors();
        }
        ImGui::PopStyleColor(2);

        ImGui::Spacing();
        ImGui::Spacing();

        // Live pixel info with modern styling
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.6f, 0.2f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("📍 LIVE PIXEL INFO");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        int screenWidth = GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = GetSystemMetrics(SM_CYSCREEN);
        COLORREF centerPixel = GetPixelColor(screenWidth / 2, screenHeight / 2);
        if (centerPixel != CLR_INVALID) {
            int r = GetRValue(centerPixel);
            int g = GetGValue(centerPixel);
            int b = GetBValue(centerPixel);

            // Color preview box
            ImVec4 colorPreview = ImVec4(r / 255.0f, g / 255.0f, b / 255.0f, 1.0f);
            ImGui::ColorButton("##centercolor", colorPreview, ImGuiColorEditFlags_NoTooltip, ImVec2(50, 50));
            ImGui::SameLine();
            ImGui::BeginGroup();
            ImGui::Text("Center Pixel:");
            ImGui::Text("RGB(%d, %d, %d)", r, g, b);
            ImGui::EndGroup();
        } else {
            ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "❌ Invalid pixel data");
        }

        ImGui::Spacing();
        ImGui::Spacing();

        // Learned Colors Section
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.6f, 0.2f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("🧠 LEARNED COLORS");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        if (g_colorsLearned && !g_capturedColors.empty()) {
            ImGui::Text("Captured %d colors:", (int)g_capturedColors.size());
            ImGui::Spacing();

            // Display colors in a grid (show more colors)
            int colorsPerRow = 8;
            for (size_t i = 0; i < g_capturedColors.size() && i < 40; i++) {
                const auto& color = g_capturedColors[i];
                ImVec4 colorVec = ImVec4(color.r / 255.0f, color.g / 255.0f, color.b / 255.0f, 1.0f);

                ImGui::ColorButton(("Learned##" + std::to_string(i)).c_str(), colorVec,
                                   ImGuiColorEditFlags_NoTooltip, ImVec2(30, 30));

                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("RGB(%d, %d, %d)\nCount: %d", color.r, color.g, color.b, color.count);
                }

                if ((i + 1) % colorsPerRow != 0 && i < g_capturedColors.size() - 1) {
                    ImGui::SameLine();
                }
            }
        } else {
            ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "⚠️ No colors learned yet");
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Enable triggerbot to auto-capture");
        }

        ImGui::Spacing();
        ImGui::Spacing();

        // Current Colors Section
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.6f, 0.2f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("🔄 CURRENT COLORS");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        if (g_config.continuousMonitoring && !g_currentColors.empty()) {
            ImGui::Text("Detecting %d colors:", (int)g_currentColors.size());
            ImGui::Spacing();

            // Display current colors in a grid (show more colors)
            int colorsPerRow = 8;
            for (size_t i = 0; i < g_currentColors.size() && i < 40; i++) {
                const auto& color = g_currentColors[i];
                ImVec4 colorVec = ImVec4(color.r / 255.0f, color.g / 255.0f, color.b / 255.0f, 1.0f);

                ImGui::ColorButton(("Current##" + std::to_string(i)).c_str(), colorVec,
                                   ImGuiColorEditFlags_NoTooltip, ImVec2(30, 30));

                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("RGB(%d, %d, %d)\nCount: %d", color.r, color.g, color.b, color.count);
                }

                if ((i + 1) % colorsPerRow != 0 && i < g_currentColors.size() - 1) {
                    ImGui::SameLine();
                }
            }

            ImGui::Spacing();

            // Enemy detection status with brightness info
            if (g_colorsLearned) {
                float baselineBrightness = CalculateAverageBrightness(g_capturedColors);
                float currentBrightness = CalculateAverageBrightness(g_currentColors);
                float brightnessDiff = abs(currentBrightness - baselineBrightness);

                ImGui::Text("Baseline: %.0f, Current: %.0f", baselineBrightness, currentBrightness);
                ImGui::Text("Brightness Diff: %.0f", brightnessDiff);

                if (g_colorChangeDetected) {
                    ImGui::TextColored(ImVec4(1.0f, 0.2f, 0.2f, 1.0f), "🎯 ENEMY DETECTED!");
                } else {
                    ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.2f, 1.0f), "🟢 Clear area");
                }
            }
        } else if (g_config.continuousMonitoring) {
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "⏳ Monitoring colors...");
        } else {
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "💤 Monitoring disabled");
        }

        ImGui::Spacing();
        ImGui::Spacing();

        // Status Section
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.6f, 0.2f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("📊 STATUS");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        // Status indicators with icons and colors
        ImGui::Text("Triggerbot:");
        ImGui::SameLine();
        if (g_config.enabled) {
            ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.2f, 1.0f), "🟢 ENABLED");
        } else {
            ImGui::TextColored(ImVec4(1.0f, 0.2f, 0.2f, 1.0f), "🔴 DISABLED");
        }

        ImGui::Text("Target Active:");
        ImGui::SameLine();
        if (g_triggerbotActive) {
            ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.2f, 1.0f), "🎯 YES");
        } else {
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "❌ NO");
        }

        ImGui::Text("Colors Learned:");
        ImGui::SameLine();
        if (g_colorsLearned) {
            ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.2f, 1.0f), "✅ YES (%d)", (int)g_capturedColors.size());
        } else {
            ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "⚠️ NO");
        }

        bool activationPressed = (GetAsyncKeyState(g_config.activationKey) & 0x8000) != 0;
        ImGui::Text("Key Held:");
        ImGui::SameLine();
        if (activationPressed) {
            ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.2f, 1.0f), "🔘 YES");
        } else {
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "⭕ NO");
        }

        if (g_config.continuousMonitoring) {
            bool monitoringActive = g_config.monitoringMode == 1 || activationPressed;
            ImGui::Text("Monitoring:");
            ImGui::SameLine();
            if (monitoringActive) {
                ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.2f, 1.0f), "👁️ ACTIVE");
            } else {
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "💤 IDLE");
            }
        }

        if (g_config.shootOnColorChange) {
            ImGui::Text("Enemy Detection:");
            ImGui::SameLine();
            if (g_colorChangeDetected) {
                ImGui::TextColored(ImVec4(1.0f, 0.2f, 0.2f, 1.0f), "🔫 SHOOTING");
            } else if (g_stabilityCounter > 0) {
                ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "⏳ TRACKING (%d/%d)", g_stabilityCounter, g_config.stabilityFrames);
            } else if (g_colorsLearned) {
                ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.2f, 1.0f), "👁️ WATCHING");
            } else {
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "⏸️ PRESS KEY TO CAPTURE");
            }
        }

        ImGui::EndChild();

        // Bottom section spanning full width
        ImGui::Columns(1);
        ImGui::Spacing();

        // Shoot Mode and Visual Settings in bottom row
        ImGui::Columns(2, "BottomColumns", true);

        // Shoot Mode Section
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.8f, 0.3f, 0.8f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("🔫 SHOOT MODE");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        const char* shootModes[] = { "Single Shot", "Burst", "Auto" };
        ImGui::Combo("##shootmode", &g_config.shootMode, shootModes, 3);

        ImGui::Spacing();
        ImGui::Text("Shoot Button:");
        const char* shootButtons[] = { "Left Mouse", "Right Mouse", "Middle Mouse" };
        int shootButtonIndex = 0;
        if (g_config.shootButton == VK_RBUTTON) shootButtonIndex = 1;
        else if (g_config.shootButton == VK_MBUTTON) shootButtonIndex = 2;

        if (ImGui::Combo("##shootbutton", &shootButtonIndex, shootButtons, 3)) {
            switch (shootButtonIndex) {
                case 0: g_config.shootButton = VK_LBUTTON; break;
                case 1: g_config.shootButton = VK_RBUTTON; break;
                case 2: g_config.shootButton = VK_MBUTTON; break;
            }
        }

        if (g_config.shootMode == 1) {
            ImGui::Spacing();
            ImGui::Text("Burst Count:");
            ImGui::SliderInt("##burstcount", &g_config.burstCount, 2, 10);
            ImGui::Text("Burst Delay:");
            ImGui::SliderInt("##burstdelay", &g_config.burstDelay, 50, 500);
            ImGui::SameLine(); ImGui::Text("ms");
        }

        ImGui::NextColumn();

        // Visual Settings Section
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.8f, 0.3f, 0.8f, 1.0f));
        ImGui::SetWindowFontScale(1.1f);
        ImGui::Text("👁️ VISUAL SETTINGS");
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopStyleColor();
        ImGui::Separator();
        ImGui::Spacing();

        ImGui::Checkbox("Show Detection Overlay", &g_config.showCrosshair);
        ImGui::Checkbox("Show Status Panel", &g_config.showStatus);

        ImGui::Columns(1);

        // Footer with version info
        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();
        ImGui::TextColored(ImVec4(0.5f, 0.5f, 0.5f, 1.0f), "Triggerbot v2.0 - Advanced Color Detection System");

        // Restore style variables
        ImGui::PopStyleVar(3);
        ImGui::End();

        // Draw detection area overlay if enabled
        if (g_config.showCrosshair && g_config.enabled) {
            // Create a transparent fullscreen overlay window
            ImGui::SetNextWindowPos(ImVec2(0, 0));
            ImGui::SetNextWindowSize(ImGui::GetIO().DisplaySize);

            ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));
            ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0);
            ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0, 0, 0, 0)); // Fully transparent background

            ImGui::Begin("##DetectionOverlay", nullptr,
                ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
                ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar |
                ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoInputs |
                ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoFocusOnAppearing |
                ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_AlwaysAutoResize);

            ImDrawList* draw_list = ImGui::GetWindowDrawList();

            // Get actual screen center coordinates relative to the window
            ImVec2 windowPos = ImGui::GetWindowPos();
            float centerX = ImGui::GetIO().DisplaySize.x * 0.5f;
            float centerY = ImGui::GetIO().DisplaySize.y * 0.5f;

            // Calculate detection box bounds
            float halfBox = g_config.pixelBoxSize * 0.5f;
            ImVec2 box_min = ImVec2(centerX - halfBox, centerY - halfBox);
            ImVec2 box_max = ImVec2(centerX + halfBox, centerY + halfBox);

            // Always draw a visible white outline for the detection area
            ImU32 outlineColor = IM_COL32(255, 255, 255, 255); // Solid white outline
            draw_list->AddRect(box_min, box_max, outlineColor, 0.0f, 0, 2.0f);

            // Add inner status-colored border
            bool activationPressed = (GetAsyncKeyState(g_config.activationKey) & 0x8000) != 0;
            bool monitoringActive = g_config.continuousMonitoring && (g_config.monitoringMode == 1 || activationPressed);

            ImU32 statusColor;
            if (!g_colorsLearned) {
                statusColor = IM_COL32(255, 255, 0, 180); // Yellow if no colors learned
            } else if (!monitoringActive) {
                statusColor = IM_COL32(128, 128, 128, 180); // Gray if not monitoring
            } else if (g_colorChangeDetected) {
                statusColor = IM_COL32(255, 165, 0, 180); // Orange if colors changed
            } else if (g_triggerbotActive) {
                statusColor = IM_COL32(0, 255, 0, 180); // Green if target detected
            } else {
                statusColor = IM_COL32(255, 0, 0, 180); // Red if enabled but no target
            }

            // Draw inner status border
            ImVec2 inner_min = ImVec2(box_min.x + 2, box_min.y + 2);
            ImVec2 inner_max = ImVec2(box_max.x - 2, box_max.y - 2);
            draw_list->AddRect(inner_min, inner_max, statusColor, 0.0f, 0, 1.0f);

            // Draw crosshair at center
            ImU32 crosshairColor = IM_COL32(255, 255, 255, 255);
            draw_list->AddLine(ImVec2(centerX - 10, centerY), ImVec2(centerX + 10, centerY), crosshairColor, 1.5f);
            draw_list->AddLine(ImVec2(centerX, centerY - 10), ImVec2(centerX, centerY + 10), crosshairColor, 1.5f);

            // Draw center dot
            draw_list->AddCircleFilled(ImVec2(centerX, centerY), 1.5f, crosshairColor);

            // Add size label above the detection box
            char sizeLabel[32];
            sprintf_s(sizeLabel, "%dx%d", g_config.pixelBoxSize, g_config.pixelBoxSize);
            ImVec2 textSize = ImGui::CalcTextSize(sizeLabel);
            ImVec2 textPos = ImVec2(centerX - textSize.x * 0.5f, box_min.y - textSize.y - 5);

            // Draw text background for better visibility
            ImVec2 textBgMin = ImVec2(textPos.x - 3, textPos.y - 1);
            ImVec2 textBgMax = ImVec2(textPos.x + textSize.x + 3, textPos.y + textSize.y + 1);
            draw_list->AddRectFilled(textBgMin, textBgMax, IM_COL32(0, 0, 0, 180));
            draw_list->AddRect(textBgMin, textBgMax, IM_COL32(255, 255, 255, 255), 0.0f, 0, 1.0f);

            // Draw the text
            draw_list->AddText(textPos, IM_COL32(255, 255, 255, 255), sizeLabel);

            // Add corner markers for better visibility
            float cornerSize = 8.0f;
            // Top-left corner
            draw_list->AddLine(ImVec2(box_min.x, box_min.y), ImVec2(box_min.x + cornerSize, box_min.y), outlineColor, 3.0f);
            draw_list->AddLine(ImVec2(box_min.x, box_min.y), ImVec2(box_min.x, box_min.y + cornerSize), outlineColor, 3.0f);
            // Top-right corner
            draw_list->AddLine(ImVec2(box_max.x, box_min.y), ImVec2(box_max.x - cornerSize, box_min.y), outlineColor, 3.0f);
            draw_list->AddLine(ImVec2(box_max.x, box_min.y), ImVec2(box_max.x, box_min.y + cornerSize), outlineColor, 3.0f);
            // Bottom-left corner
            draw_list->AddLine(ImVec2(box_min.x, box_max.y), ImVec2(box_min.x + cornerSize, box_max.y), outlineColor, 3.0f);
            draw_list->AddLine(ImVec2(box_min.x, box_max.y), ImVec2(box_min.x, box_max.y - cornerSize), outlineColor, 3.0f);
            // Bottom-right corner
            draw_list->AddLine(ImVec2(box_max.x, box_max.y), ImVec2(box_max.x - cornerSize, box_max.y), outlineColor, 3.0f);
            draw_list->AddLine(ImVec2(box_max.x, box_max.y), ImVec2(box_max.x, box_max.y - cornerSize), outlineColor, 3.0f);

            ImGui::End();
            ImGui::PopStyleColor();
            ImGui::PopStyleVar(2);
        }

        // Rendering
        ImGui::Render();
        const float clear_color[4] = { 0.45f, 0.55f, 0.60f, 1.00f };
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
        g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        g_pSwapChain->Present(1, 0); // Present with vsync
    }

    // Cleanup
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    DestroyWindow(g_hWnd);
    UnregisterClass(_T("TriggerbotImGuiClass"), hInstance);

    return 0;
}

// Helper functions for DirectX 11 device and render target creation/cleanup
bool CreateDeviceD3D(HWND hWnd) {
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
#ifdef _DEBUG
    createDeviceFlags |= D3D11_CREATE_DEVICE_DEBUG;
#endif

    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = {
        D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0,
    };
    if (D3D11CreateDeviceAndSwapChain(NULL, D3D_DRIVER_TYPE_HARDWARE, NULL,
        createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd,
        &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext) != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void CreateRenderTarget() {
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, NULL, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget() {
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = NULL;
    }
}

void CleanupDeviceD3D() {
    CleanupRenderTarget();
    if (g_pSwapChain) {
        g_pSwapChain->Release();
        g_pSwapChain = NULL;
    }
    if (g_pd3dDeviceContext) {
        g_pd3dDeviceContext->Release();
        g_pd3dDeviceContext = NULL;
    }
    if (g_pd3dDevice) {
        g_pd3dDevice->Release();
        g_pd3dDevice = NULL;
    }
}

// Win32 message handler
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    extern LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg) {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED) {
            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
            CreateRenderTarget();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}

// Triggerbot implementation functions
COLORREF GetPixelColor(int x, int y) {
    HDC screenDC = GetDC(NULL);
    COLORREF color = GetPixel(screenDC, x, y);
    ReleaseDC(NULL, screenDC);
    return color;
}

void CaptureColorsInBox() {
    g_capturedColors.clear();

    // Get primary monitor screen dimensions
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    int centerX = screenWidth / 2;
    int centerY = screenHeight / 2;

    // Create device context for the entire screen
    HDC screenDC = GetDC(NULL);
    HDC memDC = CreateCompatibleDC(screenDC);

    // Calculate detection box bounds
    int halfBox = g_config.pixelBoxSize / 2;
    int left = centerX - halfBox;
    int top = centerY - halfBox;
    int right = centerX + halfBox;
    int bottom = centerY + halfBox;

    // Ensure bounds are within screen
    left = max(0, left);
    top = max(0, top);
    right = min(screenWidth - 1, right);
    bottom = min(screenHeight - 1, bottom);

    // Collect all colors in the detection box
    std::vector<COLORREF> allColors;

    for (int x = left; x <= right; x++) {
        for (int y = top; y <= bottom; y++) {
            COLORREF color = GetPixel(screenDC, x, y);
            if (color != CLR_INVALID) {
                allColors.push_back(color);
            }
        }
    }

    // Clean up
    DeleteDC(memDC);
    ReleaseDC(NULL, screenDC);

    // Count occurrences of each color
    for (COLORREF color : allColors) {
        int r = GetRValue(color);
        int g = GetGValue(color);
        int b = GetBValue(color);

        // Find if this color already exists in captured colors (tight grouping for baseline)
        bool found = false;
        for (auto& captured : g_capturedColors) {
            if (abs(captured.r - r) <= 8 && abs(captured.g - g) <= 8 && abs(captured.b - b) <= 8) {
                captured.count++;
                found = true;
                break;
            }
        }

        if (!found) {
            CapturedColor newColor;
            newColor.r = r;
            newColor.g = g;
            newColor.b = b;
            newColor.count = 1;
            g_capturedColors.push_back(newColor);
        }
    }

    // Sort by count (most frequent first) and keep only significant colors
    std::sort(g_capturedColors.begin(), g_capturedColors.end(),
              [](const CapturedColor& a, const CapturedColor& b) {
                  return a.count > b.count;
              });

    // Remove colors that appear less than threshold times
    g_capturedColors.erase(
        std::remove_if(g_capturedColors.begin(), g_capturedColors.end(),
                       [](const CapturedColor& c) { return c.count < g_config.captureThreshold; }),
        g_capturedColors.end());

    // Limit to maximum number of colors
    if (g_capturedColors.size() > g_config.maxColors) {
        g_capturedColors.resize(g_config.maxColors);
    }

    g_colorsLearned = !g_capturedColors.empty();
    g_lastColorUpdate = std::chrono::steady_clock::now();
}

bool IsTargetColor(COLORREF color) {
    if (!g_colorsLearned || g_capturedColors.empty()) {
        return false;
    }

    int r = GetRValue(color);
    int g = GetGValue(color);
    int b = GetBValue(color);

    int tolerance = (int)(g_config.colorTolerance * 255);

    // Check if the color matches any of the captured target colors
    for (const auto& targetColor : g_capturedColors) {
        if (abs(r - targetColor.r) <= tolerance &&
            abs(g - targetColor.g) <= tolerance &&
            abs(b - targetColor.b) <= tolerance) {
            return true;
        }
    }

    return false;
}

void ResetCapturedColors() {
    g_capturedColors.clear();
    g_currentColors.clear();
    g_previousColors.clear();
    g_colorsLearned = false;
    g_colorChangeDetected = false;
    g_stabilityCounter = 0;
}

void MonitorColorsInBox() {
    if (!g_config.enabled || !g_config.continuousMonitoring) return;

    // Check monitoring mode and activation key status
    if (g_config.monitoringMode == 0) { // Only when activation key held
        bool activationPressed = (GetAsyncKeyState(g_config.activationKey) & 0x8000) != 0;
        if (!activationPressed) {
            // Clear current colors when not monitoring to avoid stale data
            if (!g_currentColors.empty()) {
                g_currentColors.clear();
                g_colorChangeDetected = false;
            }
            return; // Don't monitor when key is not pressed
        }
    }
    // If monitoringMode == 1 (constant), always monitor when enabled

    // Check if enough time has passed since last monitoring update
    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastUpdate = std::chrono::duration_cast<std::chrono::milliseconds>(now - g_lastMonitoringUpdate).count();
    if (timeSinceLastUpdate < g_config.monitoringInterval) {
        return; // Skip this update to reduce lag
    }
    g_lastMonitoringUpdate = now;

    // Get screen center (crosshair position)
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    int centerX = screenWidth / 2;
    int centerY = screenHeight / 2;

    // Calculate detection box bounds
    int halfBox = g_config.pixelBoxSize / 2;
    int left = centerX - halfBox;
    int top = centerY - halfBox;
    int right = centerX + halfBox;
    int bottom = centerY + halfBox;

    // Ensure bounds are within screen
    left = max(0, left);
    top = max(0, top);
    right = min(screenWidth - 1, right);
    bottom = min(screenHeight - 1, bottom);

    // Clear current colors
    g_currentColors.clear();

    // Create device context for the entire screen
    HDC screenDC = GetDC(NULL);

    // Collect colors using sampling for performance
    std::vector<COLORREF> allColors;

    // Sample pixels based on sample rate to improve performance
    for (int x = left; x <= right; x += g_config.sampleRate) {
        for (int y = top; y <= bottom; y += g_config.sampleRate) {
            COLORREF color = GetPixel(screenDC, x, y);
            if (color != CLR_INVALID) {
                allColors.push_back(color);
            }
        }
    }

    // Clean up
    ReleaseDC(NULL, screenDC);

    // Early exit if no colors found
    if (allColors.empty()) return;

    // Count occurrences of each color (optimized)
    for (COLORREF color : allColors) {
        int r = GetRValue(color);
        int g = GetGValue(color);
        int b = GetBValue(color);

        // Find if this color already exists in current colors (use tight grouping for abrupt changes)
        bool found = false;
        for (auto& current : g_currentColors) {
            if (abs(current.r - r) <= 10 && abs(current.g - g) <= 10 && abs(current.b - b) <= 10) {
                current.count++;
                found = true;
                break;
            }
        }

        if (!found) {
            CapturedColor newColor;
            newColor.r = r;
            newColor.g = g;
            newColor.b = b;
            newColor.count = 1;
            g_currentColors.push_back(newColor);
        }
    }

    // Sort by count (most frequent first) and limit to max colors
    std::sort(g_currentColors.begin(), g_currentColors.end(),
              [](const CapturedColor& a, const CapturedColor& b) {
                  return a.count > b.count;
              });

    // Remove colors that appear less than threshold times (adjusted for sampling)
    int adjustedThreshold = max(1, g_config.captureThreshold / (g_config.sampleRate * g_config.sampleRate));
    g_currentColors.erase(
        std::remove_if(g_currentColors.begin(), g_currentColors.end(),
                       [adjustedThreshold](const CapturedColor& c) { return c.count < adjustedThreshold; }),
        g_currentColors.end());

    // Limit to maximum number of colors
    if (g_currentColors.size() > g_config.maxColors) {
        g_currentColors.resize(g_config.maxColors);
    }
}

float CalculateColorSimilarity(const std::vector<CapturedColor>& colors1, const std::vector<CapturedColor>& colors2) {
    if (colors1.empty() && colors2.empty()) return 1.0f;
    if (colors1.empty() || colors2.empty()) return 0.0f;

    float totalSimilarity = 0.0f;
    int matches = 0;

    // Compare each color in colors1 with colors in colors2
    for (const auto& color1 : colors1) {
        float bestMatch = 0.0f;
        for (const auto& color2 : colors2) {
            // Calculate color distance (0-1, where 1 is identical)
            float rDiff = abs(color1.r - color2.r) / 255.0f;
            float gDiff = abs(color1.g - color2.g) / 255.0f;
            float bDiff = abs(color1.b - color2.b) / 255.0f;
            float distance = (float)(sqrt(rDiff * rDiff + gDiff * gDiff + bDiff * bDiff) / sqrt(3.0f));
            float similarity = 1.0f - distance;

            if (similarity > bestMatch) {
                bestMatch = similarity;
            }
        }
        totalSimilarity += bestMatch;
        matches++;
    }

    return matches > 0 ? totalSimilarity / matches : 0.0f;
}

// Calculate average brightness of a color set
float CalculateAverageBrightness(const std::vector<CapturedColor>& colors) {
    if (colors.empty()) return 0.0f;

    float totalBrightness = 0.0f;
    int totalPixels = 0;

    for (const auto& color : colors) {
        // Calculate perceived brightness using standard formula
        float brightness = (0.299f * color.r + 0.587f * color.g + 0.114f * color.b);
        totalBrightness += brightness * color.count;
        totalPixels += color.count;
    }

    return totalPixels > 0 ? totalBrightness / totalPixels : 0.0f;
}

bool DetectColorChange() {
    if (!g_colorsLearned || g_currentColors.empty()) return false;

    // Calculate average brightness of baseline vs current
    float baselineBrightness = CalculateAverageBrightness(g_capturedColors);
    float currentBrightness = CalculateAverageBrightness(g_currentColors);

    // Calculate brightness difference
    float brightnessDifference = abs(currentBrightness - baselineBrightness);

    // Check if brightness change is significant enough (enemies are darker/brighter than maps)
    bool significantBrightnessChange = brightnessDifference >= g_config.brightnessThreshold;

    // Also check color similarity for additional validation
    float similarity = CalculateColorSimilarity(g_capturedColors, g_currentColors);
    bool significantColorChange = similarity < (1.0f - g_config.changeThreshold);

    // Require both brightness and color changes for enemy detection
    return significantBrightnessChange && significantColorChange;
}

bool DetectTarget() {
    if (!g_config.enabled || !g_colorsLearned) return false;

    // Get screen center (crosshair position)
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    int centerX = screenWidth / 2;
    int centerY = screenHeight / 2;

    // Calculate detection box bounds
    int halfBox = g_config.pixelBoxSize / 2;
    int left = centerX - halfBox;
    int top = centerY - halfBox;
    int right = centerX + halfBox;
    int bottom = centerY + halfBox;

    // Ensure bounds are within screen
    left = max(0, left);
    top = max(0, top);
    right = min(screenWidth - 1, right);
    bottom = min(screenHeight - 1, bottom);

    // Check pixels in the detection box with sampling for performance
    HDC screenDC = GetDC(NULL);
    int sampleRate = max(1, g_config.sampleRate / 2); // Use finer sampling for target detection

    for (int x = left; x <= right; x += sampleRate) {
        for (int y = top; y <= bottom; y += sampleRate) {
            COLORREF color = GetPixel(screenDC, x, y);
            if (color != CLR_INVALID && IsTargetColor(color)) {
                ReleaseDC(NULL, screenDC);
                return true;
            }
        }
    }
    ReleaseDC(NULL, screenDC);
    return false;
}

void SimulateMouseClick() {
    // Determine mouse button flags based on shoot button
    DWORD downFlag, upFlag;
    switch (g_config.shootButton) {
        case VK_LBUTTON:
            downFlag = MOUSEEVENTF_LEFTDOWN;
            upFlag = MOUSEEVENTF_LEFTUP;
            break;
        case VK_RBUTTON:
            downFlag = MOUSEEVENTF_RIGHTDOWN;
            upFlag = MOUSEEVENTF_RIGHTUP;
            break;
        case VK_MBUTTON:
            downFlag = MOUSEEVENTF_MIDDLEDOWN;
            upFlag = MOUSEEVENTF_MIDDLEUP;
            break;
        default:
            downFlag = MOUSEEVENTF_LEFTDOWN;
            upFlag = MOUSEEVENTF_LEFTUP;
            break;
    }

    if (g_config.shootMode == 0) {
        // Single shot
        INPUT input = {0};
        input.type = INPUT_MOUSE;
        input.mi.dwFlags = downFlag;
        SendInput(1, &input, sizeof(INPUT));

        std::this_thread::sleep_for(std::chrono::milliseconds(g_config.shootTime));

        input.mi.dwFlags = upFlag;
        SendInput(1, &input, sizeof(INPUT));
    }
    else if (g_config.shootMode == 1) {
        // Burst mode
        for (int i = 0; i < g_config.burstCount; i++) {
            INPUT input = {0};
            input.type = INPUT_MOUSE;
            input.mi.dwFlags = downFlag;
            SendInput(1, &input, sizeof(INPUT));

            std::this_thread::sleep_for(std::chrono::milliseconds(g_config.shootTime));

            input.mi.dwFlags = upFlag;
            SendInput(1, &input, sizeof(INPUT));

            if (i < g_config.burstCount - 1) {
                std::this_thread::sleep_for(std::chrono::milliseconds(g_config.burstDelay));
            }
        }
    }
    else if (g_config.shootMode == 2) {
        // Auto mode - single click but will be called repeatedly
        INPUT input = {0};
        input.type = INPUT_MOUSE;
        input.mi.dwFlags = downFlag;
        SendInput(1, &input, sizeof(INPUT));

        std::this_thread::sleep_for(std::chrono::milliseconds(g_config.shootTime));

        input.mi.dwFlags = upFlag;
        SendInput(1, &input, sizeof(INPUT));
    }
}

void UpdateTriggerbot() {
    // Check if triggerbot was just enabled (toggled on)
    if (g_config.enabled && !g_config.wasEnabled) {
        // Reset state when enabled
        g_config.keyWasPressed = false;
        g_colorsLearned = false;
        g_stabilityCounter = 0;
    }

    // Update previous state
    g_config.wasEnabled = g_config.enabled;

    if (!g_config.enabled) {
        g_triggerbotActive = false;
        g_colorChangeDetected = false;
        g_config.keyWasPressed = false;
        return;
    }

    // Check activation key state
    bool activationPressed = (GetAsyncKeyState(g_config.activationKey) & 0x8000) != 0;

    // Capture baseline colors when key is first pressed
    if (g_config.captureOnKeyPress && activationPressed && !g_config.keyWasPressed) {
        CaptureColorsInBox();
        g_config.keyWasPressed = true;
        g_stabilityCounter = 0; // Reset stability counter on new capture
    }

    // Reset key state when released
    if (!activationPressed) {
        g_config.keyWasPressed = false;
        g_stabilityCounter = 0; // Reset stability when key released
    }

    // Monitor for abrupt color changes when key is held and colors are learned
    if (g_config.continuousMonitoring && activationPressed && g_colorsLearned) {
        MonitorColorsInBox();

        // Check for abrupt color changes (enemies appearing)
        bool currentChangeDetected = DetectColorChange();

        // Stability checking: require fewer consecutive detections for abrupt changes
        if (currentChangeDetected) {
            g_stabilityCounter++;
        } else {
            g_stabilityCounter = 0; // Reset counter if no change detected
        }

        // Only trigger if we have enough consecutive detections
        bool stableChangeDetected = g_stabilityCounter >= g_config.stabilityFrames;
        bool previousChangeState = g_colorChangeDetected;
        g_colorChangeDetected = stableChangeDetected;

        // Shoot on stable abrupt change if enabled and change just became stable
        if (g_config.shootOnColorChange && stableChangeDetected && !previousChangeState) {
            auto now = std::chrono::steady_clock::now();
            auto timeSinceLastShot = std::chrono::duration_cast<std::chrono::milliseconds>(now - g_lastShot).count();

            // Apply reaction delay before shooting on abrupt change
            if (timeSinceLastShot >= g_config.reactionDelay + g_config.shootTime + 100) {
                SimulateMouseClick();
                g_lastShot = now;
                g_triggerbotActive = true;
                g_stabilityCounter = 0; // Reset after shooting to prevent spam
            }
        }
    }

    // Check if activation key is pressed
    bool activationPressed = (GetAsyncKeyState(g_config.activationKey) & 0x8000) != 0;

    if (activationPressed && DetectTarget()) {
        auto now = std::chrono::steady_clock::now();
        auto timeSinceLastDetection = std::chrono::duration_cast<std::chrono::milliseconds>(now - g_lastDetection).count();
        auto timeSinceLastShot = std::chrono::duration_cast<std::chrono::milliseconds>(now - g_lastShot).count();

        // Apply reaction delay
        if (timeSinceLastDetection >= g_config.reactionDelay && timeSinceLastShot >= g_config.shootTime + 50) {
            SimulateMouseClick();
            g_lastShot = now;
            g_triggerbotActive = true;
        }
        g_lastDetection = now;
    } else {
        g_triggerbotActive = false;
    }
}

const char* GetKeyName(int vk) {
    switch (vk) {
        case VK_LBUTTON: return "Left Mouse";
        case VK_RBUTTON: return "Right Mouse";
        case VK_MBUTTON: return "Middle Mouse";
        case VK_XBUTTON1: return "Mouse 4";
        case VK_XBUTTON2: return "Mouse 5";
        case VK_SHIFT: return "Shift";
        case VK_CONTROL: return "Ctrl";
        case VK_MENU: return "Alt";
        case VK_SPACE: return "Space";
        case VK_RETURN: return "Enter";
        case VK_TAB: return "Tab";
        case VK_ESCAPE: return "Escape";
        case VK_F1: return "F1";
        case VK_F2: return "F2";
        case VK_F3: return "F3";
        case VK_F4: return "F4";
        case VK_F5: return "F5";
        case VK_F6: return "F6";
        case VK_F7: return "F7";
        case VK_F8: return "F8";
        case VK_F9: return "F9";
        case VK_F10: return "F10";
        case VK_F11: return "F11";
        case VK_F12: return "F12";
        default: return "Unknown";
    }
}
