// Add ImGui includes and DirectX 11 includes
#include "imgui/imgui.h"
#include "imgui/imgui_impl_win32.h"
#include "imgui/imgui_impl_dx11.h"
#include <d3d11.h>
#include <tchar.h>
#include <windows.h>
#include <chrono>
#include <thread>

// Link necessary libraries
#pragma comment(lib, "d3d11.lib")

// Triggerbot configuration structure
struct TriggerbotConfig {
    bool enabled = false;
    int activationKey = VK_LBUTTON; // Left mouse button by default
    int pixelBoxSize = 5; // Size of the detection box around crosshair
    int reactionDelay = 50; // Delay in milliseconds before shooting
    int shootTime = 100; // How long to hold the shoot button in milliseconds
    int shootMode = 0; // 0 = Single shot, 1 = Burst, 2 = Auto
    float targetColorR = 1.0f; // Red component of target color
    float targetColorG = 0.0f; // Green component of target color
    float targetColorB = 0.0f; // Blue component of target color
    float colorTolerance = 0.1f; // Color matching tolerance
    bool showStatus = true; // Show status information
    bool showCrosshair = false; // Show detection area overlay
    int burstCount = 3; // Number of shots in burst mode
    int burstDelay = 100; // Delay between burst shots
};

// Global triggerbot configuration
static TriggerbotConfig g_config;
static bool g_triggerbotActive = false;
static std::chrono::steady_clock::time_point g_lastDetection;
static std::chrono::steady_clock::time_point g_lastShot;

// Global variables for DirectX 11
static ID3D11Device*           g_pd3dDevice = NULL;
static ID3D11DeviceContext*    g_pd3dDeviceContext = NULL;
static IDXGISwapChain*         g_pSwapChain = NULL;
static ID3D11RenderTargetView* g_mainRenderTargetView = NULL;
static HWND                   g_hWnd = NULL;

// Forward declarations
bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Triggerbot utility functions
COLORREF GetPixelColor(int x, int y);
bool IsTargetColor(COLORREF color);
bool DetectTarget();
void SimulateMouseClick();
void UpdateTriggerbot();
const char* GetKeyName(int vk);

// New Win32 window creation for ImGui
HWND CreateAppWindow(HINSTANCE hInstance, int nCmdShow) {
    WNDCLASSEX wc = { sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0, 0,
                      GetModuleHandle(NULL), NULL, NULL, NULL, NULL,
                      _T("TriggerbotImGuiClass"), NULL };
    RegisterClassEx(&wc);

    HWND hwnd = CreateWindow(wc.lpszClassName, _T("Triggerbot ImGui Window"),
                             WS_OVERLAPPEDWINDOW, 100, 100, 800, 600,
                             NULL, NULL, wc.hInstance, NULL);

    ShowWindow(hwnd, nCmdShow);
    UpdateWindow(hwnd);

    return hwnd;
}

// Main function with ImGui integration
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE, LPSTR, int nCmdShow) {
    // Create application window
    g_hWnd = CreateAppWindow(hInstance, nCmdShow);
    if (!CreateDeviceD3D(g_hWnd)) {
        CleanupDeviceD3D();
        UnregisterClass(_T("TriggerbotImGuiClass"), hInstance);
        return 1;
    }

    // Setup ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;

    // Setup ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(g_hWnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    // Main loop
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }

        // Start ImGui frame
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Update triggerbot logic
        UpdateTriggerbot();

        // Triggerbot UI
        ImGui::Begin("Triggerbot Settings");

        // Main enable/disable toggle
        ImGui::Checkbox("Enable Triggerbot", &g_config.enabled);
        ImGui::Separator();

        // Activation key selection
        ImGui::Text("Activation Key: %s", GetKeyName(g_config.activationKey));
        if (ImGui::Button("Change Activation Key")) {
            ImGui::OpenPopup("Key Selection");
        }

        if (ImGui::BeginPopup("Key Selection")) {
            if (ImGui::Selectable("Left Mouse")) g_config.activationKey = VK_LBUTTON;
            if (ImGui::Selectable("Right Mouse")) g_config.activationKey = VK_RBUTTON;
            if (ImGui::Selectable("Middle Mouse")) g_config.activationKey = VK_MBUTTON;
            if (ImGui::Selectable("Mouse 4")) g_config.activationKey = VK_XBUTTON1;
            if (ImGui::Selectable("Mouse 5")) g_config.activationKey = VK_XBUTTON2;
            if (ImGui::Selectable("Shift")) g_config.activationKey = VK_SHIFT;
            if (ImGui::Selectable("Ctrl")) g_config.activationKey = VK_CONTROL;
            if (ImGui::Selectable("Alt")) g_config.activationKey = VK_MENU;
            if (ImGui::Selectable("Space")) g_config.activationKey = VK_SPACE;
            ImGui::EndPopup();
        }

        ImGui::Separator();

        // Detection settings
        ImGui::Text("Detection Settings");
        ImGui::SliderInt("Pixel Box Size", &g_config.pixelBoxSize, 1, 20);
        ImGui::SliderInt("Reaction Delay (ms)", &g_config.reactionDelay, 0, 500);
        ImGui::SliderInt("Shoot Time (ms)", &g_config.shootTime, 10, 1000);

        ImGui::Separator();

        // Target color settings
        ImGui::Text("Target Color");
        ImGui::ColorEdit3("Color", &g_config.targetColorR);
        ImGui::SliderFloat("Color Tolerance", &g_config.colorTolerance, 0.0f, 1.0f);

        ImGui::Separator();

        // Shoot mode selection
        ImGui::Text("Shoot Mode");
        const char* shootModes[] = { "Single Shot", "Burst", "Auto" };
        ImGui::Combo("Mode", &g_config.shootMode, shootModes, 3);

        // Burst mode settings (only show if burst mode is selected)
        if (g_config.shootMode == 1) {
            ImGui::SliderInt("Burst Count", &g_config.burstCount, 2, 10);
            ImGui::SliderInt("Burst Delay (ms)", &g_config.burstDelay, 50, 500);
        }

        ImGui::Separator();

        // Visual settings
        ImGui::Text("Visual Settings");
        ImGui::Checkbox("Show Detection Area", &g_config.showCrosshair);

        ImGui::Separator();

        // Status display
        ImGui::Checkbox("Show Status", &g_config.showStatus);

        if (g_config.showStatus) {
            ImGui::Separator();
            ImGui::Text("Status Information");
            ImGui::Text("Triggerbot: %s", g_config.enabled ? "ENABLED" : "DISABLED");
            ImGui::Text("Active: %s", g_triggerbotActive ? "YES" : "NO");
            ImGui::Text("Activation Key: %s", GetKeyName(g_config.activationKey));

            // Show current pixel color at screen center
            int screenWidth = GetSystemMetrics(SM_CXSCREEN);
            int screenHeight = GetSystemMetrics(SM_CYSCREEN);
            COLORREF centerColor = GetPixelColor(screenWidth / 2, screenHeight / 2);
            int r = GetRValue(centerColor);
            int g = GetGValue(centerColor);
            int b = GetBValue(centerColor);
            ImGui::Text("Center Pixel: RGB(%d, %d, %d)", r, g, b);

            bool isTarget = IsTargetColor(centerColor);
            ImGui::Text("Target Detected: %s", isTarget ? "YES" : "NO");
        }

        ImGui::End();

        // Draw detection area overlay if enabled
        if (g_config.showCrosshair && g_config.enabled) {
            ImGui::SetNextWindowPos(ImVec2(0, 0));
            ImGui::SetNextWindowSize(ImGui::GetIO().DisplaySize);
            ImGui::Begin("Overlay", nullptr,
                ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
                ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar |
                ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoInputs |
                ImGuiWindowFlags_NoBackground);

            ImDrawList* draw_list = ImGui::GetWindowDrawList();

            // Get screen center
            ImVec2 center = ImVec2(ImGui::GetIO().DisplaySize.x * 0.5f, ImGui::GetIO().DisplaySize.y * 0.5f);
            float halfBox = g_config.pixelBoxSize * 0.5f;

            // Draw detection box
            ImVec2 box_min = ImVec2(center.x - halfBox, center.y - halfBox);
            ImVec2 box_max = ImVec2(center.x + halfBox, center.y + halfBox);

            // Color based on detection status
            ImU32 color = g_triggerbotActive ? IM_COL32(0, 255, 0, 128) : IM_COL32(255, 0, 0, 128);
            draw_list->AddRect(box_min, box_max, color, 0.0f, 0, 2.0f);

            // Draw crosshair
            draw_list->AddLine(ImVec2(center.x - 10, center.y), ImVec2(center.x + 10, center.y), IM_COL32(255, 255, 255, 128), 1.0f);
            draw_list->AddLine(ImVec2(center.x, center.y - 10), ImVec2(center.x, center.y + 10), IM_COL32(255, 255, 255, 128), 1.0f);

            ImGui::End();
        }

        // Rendering
        ImGui::Render();
        const float clear_color[4] = { 0.45f, 0.55f, 0.60f, 1.00f };
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
        g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        g_pSwapChain->Present(1, 0); // Present with vsync
    }

    // Cleanup
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    DestroyWindow(g_hWnd);
    UnregisterClass(_T("TriggerbotImGuiClass"), hInstance);

    return 0;
}

// Helper functions for DirectX 11 device and render target creation/cleanup
bool CreateDeviceD3D(HWND hWnd) {
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
#ifdef _DEBUG
    createDeviceFlags |= D3D11_CREATE_DEVICE_DEBUG;
#endif

    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = {
        D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0,
    };
    if (D3D11CreateDeviceAndSwapChain(NULL, D3D_DRIVER_TYPE_HARDWARE, NULL,
        createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd,
        &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext) != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void CreateRenderTarget() {
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, NULL, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget() {
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = NULL;
    }
}

void CleanupDeviceD3D() {
    CleanupRenderTarget();
    if (g_pSwapChain) {
        g_pSwapChain->Release();
        g_pSwapChain = NULL;
    }
    if (g_pd3dDeviceContext) {
        g_pd3dDeviceContext->Release();
        g_pd3dDeviceContext = NULL;
    }
    if (g_pd3dDevice) {
        g_pd3dDevice->Release();
        g_pd3dDevice = NULL;
    }
}

// Win32 message handler
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    extern LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg) {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED) {
            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
            CreateRenderTarget();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}

// Triggerbot implementation functions
COLORREF GetPixelColor(int x, int y) {
    HDC hdc = GetDC(NULL);
    COLORREF color = GetPixel(hdc, x, y);
    ReleaseDC(NULL, hdc);
    return color;
}

bool IsTargetColor(COLORREF color) {
    int r = GetRValue(color);
    int g = GetGValue(color);
    int b = GetBValue(color);

    int targetR = (int)(g_config.targetColorR * 255);
    int targetG = (int)(g_config.targetColorG * 255);
    int targetB = (int)(g_config.targetColorB * 255);

    int tolerance = (int)(g_config.colorTolerance * 255);

    return (abs(r - targetR) <= tolerance &&
            abs(g - targetG) <= tolerance &&
            abs(b - targetB) <= tolerance);
}

bool DetectTarget() {
    if (!g_config.enabled) return false;

    // Get screen center (crosshair position)
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    int centerX = screenWidth / 2;
    int centerY = screenHeight / 2;

    // Check pixels in a box around the crosshair
    int halfBox = g_config.pixelBoxSize / 2;
    for (int x = centerX - halfBox; x <= centerX + halfBox; x++) {
        for (int y = centerY - halfBox; y <= centerY + halfBox; y++) {
            COLORREF color = GetPixelColor(x, y);
            if (IsTargetColor(color)) {
                return true;
            }
        }
    }
    return false;
}

void SimulateMouseClick() {
    if (g_config.shootMode == 0) {
        // Single shot
        INPUT input = {0};
        input.type = INPUT_MOUSE;
        input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
        SendInput(1, &input, sizeof(INPUT));

        std::this_thread::sleep_for(std::chrono::milliseconds(g_config.shootTime));

        input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
        SendInput(1, &input, sizeof(INPUT));
    }
    else if (g_config.shootMode == 1) {
        // Burst mode
        for (int i = 0; i < g_config.burstCount; i++) {
            INPUT input = {0};
            input.type = INPUT_MOUSE;
            input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
            SendInput(1, &input, sizeof(INPUT));

            std::this_thread::sleep_for(std::chrono::milliseconds(g_config.shootTime));

            input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
            SendInput(1, &input, sizeof(INPUT));

            if (i < g_config.burstCount - 1) {
                std::this_thread::sleep_for(std::chrono::milliseconds(g_config.burstDelay));
            }
        }
    }
    else if (g_config.shootMode == 2) {
        // Auto mode - single click but will be called repeatedly
        INPUT input = {0};
        input.type = INPUT_MOUSE;
        input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
        SendInput(1, &input, sizeof(INPUT));

        std::this_thread::sleep_for(std::chrono::milliseconds(g_config.shootTime));

        input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
        SendInput(1, &input, sizeof(INPUT));
    }
}

void UpdateTriggerbot() {
    if (!g_config.enabled) return;

    // Check if activation key is pressed
    bool activationPressed = (GetAsyncKeyState(g_config.activationKey) & 0x8000) != 0;

    if (activationPressed && DetectTarget()) {
        auto now = std::chrono::steady_clock::now();
        auto timeSinceLastDetection = std::chrono::duration_cast<std::chrono::milliseconds>(now - g_lastDetection).count();
        auto timeSinceLastShot = std::chrono::duration_cast<std::chrono::milliseconds>(now - g_lastShot).count();

        // Apply reaction delay
        if (timeSinceLastDetection >= g_config.reactionDelay && timeSinceLastShot >= g_config.shootTime + 50) {
            SimulateMouseClick();
            g_lastShot = now;
            g_triggerbotActive = true;
        }
        g_lastDetection = now;
    } else {
        g_triggerbotActive = false;
    }
}

const char* GetKeyName(int vk) {
    switch (vk) {
        case VK_LBUTTON: return "Left Mouse";
        case VK_RBUTTON: return "Right Mouse";
        case VK_MBUTTON: return "Middle Mouse";
        case VK_XBUTTON1: return "Mouse 4";
        case VK_XBUTTON2: return "Mouse 5";
        case VK_SHIFT: return "Shift";
        case VK_CONTROL: return "Ctrl";
        case VK_MENU: return "Alt";
        case VK_SPACE: return "Space";
        case VK_RETURN: return "Enter";
        case VK_TAB: return "Tab";
        case VK_ESCAPE: return "Escape";
        case VK_F1: return "F1";
        case VK_F2: return "F2";
        case VK_F3: return "F3";
        case VK_F4: return "F4";
        case VK_F5: return "F5";
        case VK_F6: return "F6";
        case VK_F7: return "F7";
        case VK_F8: return "F8";
        case VK_F9: return "F9";
        case VK_F10: return "F10";
        case VK_F11: return "F11";
        case VK_F12: return "F12";
        default: return "Unknown";
    }
}
