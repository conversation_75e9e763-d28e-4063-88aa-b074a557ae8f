// Simple Pixel Triggerbot - Detects pixel changes and shoots
#include "imgui/imgui.h"
#include "imgui/imgui_impl_win32.h"
#include "imgui/imgui_impl_dx11.h"
#include <d3d11.h>
#include <tchar.h>
#include <windows.h>
#include <chrono>
#include <thread>
#include <vector>

#pragma comment(lib, "d3d11.lib")

// Simple pixel data structure
struct PixelData {
    int r, g, b;
    int x, y;
};

// Simple triggerbot configuration
struct TriggerbotConfig {
    bool enabled = false;
    bool wasEnabled = false;
    int activationKey = VK_RBUTTON; // Right mouse button by default
    int pixelBoxSize = 20;
    int reactionDelay = 50;
    int shootTime = 100;
    int pixelTolerance = 30;
    int minChangedPixels = 3;
    int monitoringInterval = 30;
    bool shootOnPixelChange = true;
    int shootButton = VK_LBUTTON; // Left mouse for shooting
    bool captureOnKeyPress = true;
    bool keyWasPressed = false;
    bool showCrosshair = false;
};

// Global variables
static TriggerbotConfig g_config;
static std::vector<PixelData> g_baselinePixels;
static bool g_pixelsLearned = false;
static bool g_pixelChangeDetected = false;
static bool g_triggerbotActive = false;
static int g_changedPixelCount = 0;
static std::chrono::steady_clock::time_point g_lastPixelUpdate;
static std::chrono::steady_clock::time_point g_lastShot;

// DirectX variables
static ID3D11Device* g_pd3dDevice = nullptr;
static ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
static IDXGISwapChain* g_pSwapChain = nullptr;
static UINT g_ResizeWidth = 0, g_ResizeHeight = 0;
static ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;

// Forward declarations
bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Triggerbot functions
COLORREF GetPixelColor(int x, int y);
void CaptureBaselinePixels();
void MonitorPixelChanges();
bool DetectPixelChange();
void SimulateMouseClick();
void UpdateTriggerbot();
const char* GetKeyName(int vk);
void ResetPixelData();

// Main function
int main(int, char**)
{
    // Create application window
    WNDCLASSEXW wc = { sizeof(wc), CS_CLASSDC, WndProc, 0L, 0L, GetModuleHandle(nullptr), nullptr, nullptr, nullptr, nullptr, L"Pixel Triggerbot", nullptr };
    ::RegisterClassExW(&wc);
    HWND hwnd = ::CreateWindowW(wc.lpszClassName, L"Pixel Triggerbot", WS_OVERLAPPEDWINDOW, 100, 100, 1280, 800, nullptr, nullptr, wc.hInstance, nullptr);

    // Initialize Direct3D
    if (!CreateDeviceD3D(hwnd))
    {
        CleanupDeviceD3D();
        ::UnregisterClassW(wc.lpszClassName, wc.hInstance);
        return 1;
    }

    // Show the window
    ::ShowWindow(hwnd, SW_SHOWDEFAULT);
    ::UpdateWindow(hwnd);

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup Dear ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    // Main loop
    bool done = false;
    while (!done)
    {
        MSG msg;
        while (::PeekMessage(&msg, nullptr, 0U, 0U, PM_REMOVE))
        {
            ::TranslateMessage(&msg);
            ::DispatchMessage(&msg);
            if (msg.message == WM_QUIT)
                done = true;
        }
        if (done)
            break;

        // Handle window resize
        if (g_ResizeWidth != 0 && g_ResizeHeight != 0)
        {
            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, g_ResizeWidth, g_ResizeHeight, DXGI_FORMAT_UNKNOWN, 0);
            g_ResizeWidth = g_ResizeHeight = 0;
            CreateRenderTarget();
        }

        // Start the Dear ImGui frame
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Update triggerbot logic
        UpdateTriggerbot();

        // Simple UI
        ImGui::Begin("Simple Pixel Triggerbot");
        
        // Main enable/disable toggle
        if (ImGui::Button(g_config.enabled ? "DISABLE TRIGGERBOT" : "ENABLE TRIGGERBOT", ImVec2(200, 40))) {
            g_config.enabled = !g_config.enabled;
        }
        
        ImGui::Separator();
        
        // Basic controls
        ImGui::Text("Activation Key: %s", GetKeyName(g_config.activationKey));
        if (ImGui::BeginCombo("##ActivationKey", GetKeyName(g_config.activationKey))) {
            if (ImGui::Selectable("Right Mouse", g_config.activationKey == VK_RBUTTON))
                g_config.activationKey = VK_RBUTTON;
            if (ImGui::Selectable("Middle Mouse", g_config.activationKey == VK_MBUTTON))
                g_config.activationKey = VK_MBUTTON;
            if (ImGui::Selectable("Mouse 4", g_config.activationKey == VK_XBUTTON1))
                g_config.activationKey = VK_XBUTTON1;
            if (ImGui::Selectable("Mouse 5", g_config.activationKey == VK_XBUTTON2))
                g_config.activationKey = VK_XBUTTON2;
            if (ImGui::Selectable("Shift", g_config.activationKey == VK_SHIFT))
                g_config.activationKey = VK_SHIFT;
            if (ImGui::Selectable("Ctrl", g_config.activationKey == VK_CONTROL))
                g_config.activationKey = VK_CONTROL;
            if (ImGui::Selectable("Alt", g_config.activationKey == VK_MENU))
                g_config.activationKey = VK_MENU;
            if (ImGui::Selectable("Space", g_config.activationKey == VK_SPACE))
                g_config.activationKey = VK_SPACE;
            ImGui::EndCombo();
        }

        ImGui::SliderInt("Detection Box Size", &g_config.pixelBoxSize, 10, 100);
        ImGui::SliderInt("Pixel Tolerance", &g_config.pixelTolerance, 10, 100);
        ImGui::SliderInt("Min Changed Pixels", &g_config.minChangedPixels, 1, 20);
        ImGui::SliderInt("Reaction Delay (ms)", &g_config.reactionDelay, 0, 500);
        
        ImGui::Separator();
        
        // Capture controls
        if (ImGui::Button("Capture Baseline Pixels", ImVec2(200, 30))) {
            CaptureBaselinePixels();
        }
        if (ImGui::Button("Reset Pixels", ImVec2(200, 30))) {
            ResetPixelData();
        }
        
        ImGui::Separator();
        
        // Status
        ImGui::Text("Status: %s", g_config.enabled ? "ENABLED" : "DISABLED");
        ImGui::Text("Pixels Learned: %s (%d)", g_pixelsLearned ? "YES" : "NO", (int)g_baselinePixels.size());
        ImGui::Text("Changed Pixels: %d", g_changedPixelCount);
        ImGui::Text("Movement Detected: %s", g_pixelChangeDetected ? "YES" : "NO");
        
        ImGui::Checkbox("Show Detection Area", &g_config.showCrosshair);
        
        ImGui::End();

        // Simple overlay
        if (g_config.showCrosshair && g_config.enabled) {
            ImGui::SetNextWindowPos(ImVec2(0, 0));
            ImGui::SetNextWindowSize(ImGui::GetIO().DisplaySize);
            ImGui::Begin("##Overlay", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | 
                        ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoInputs | 
                        ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoDecoration);
            
            ImDrawList* draw_list = ImGui::GetWindowDrawList();
            float centerX = ImGui::GetIO().DisplaySize.x * 0.5f;
            float centerY = ImGui::GetIO().DisplaySize.y * 0.5f;
            float halfBox = g_config.pixelBoxSize * 0.5f;
            
            ImVec2 box_min = ImVec2(centerX - halfBox, centerY - halfBox);
            ImVec2 box_max = ImVec2(centerX + halfBox, centerY + halfBox);
            
            ImU32 color = g_pixelChangeDetected ? IM_COL32(0, 255, 0, 255) : IM_COL32(255, 0, 0, 255);
            draw_list->AddRect(box_min, box_max, color, 0.0f, 0, 2.0f);
            
            ImGui::End();
        }

        // Rendering
        ImGui::Render();
        const float clear_color_with_alpha[4] = { 0.45f, 0.55f, 0.60f, 1.00f };
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);
        g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color_with_alpha);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

        g_pSwapChain->Present(1, 0);
    }

    // Cleanup
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    ::DestroyWindow(hwnd);
    ::UnregisterClassW(wc.lpszClassName, wc.hInstance);

    return 0;
}

// Implementation functions
COLORREF GetPixelColor(int x, int y) {
    HDC screenDC = GetDC(NULL);
    COLORREF color = GetPixel(screenDC, x, y);
    ReleaseDC(NULL, screenDC);
    return color;
}

void CaptureBaselinePixels() {
    g_baselinePixels.clear();

    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    int centerX = screenWidth / 2;
    int centerY = screenHeight / 2;

    int halfBox = g_config.pixelBoxSize / 2;
    int left = centerX - halfBox;
    int top = centerY - halfBox;
    int right = centerX + halfBox;
    int bottom = centerY + halfBox;

    HDC screenDC = GetDC(NULL);

    for (int x = left; x <= right; x++) {
        for (int y = top; y <= bottom; y++) {
            COLORREF color = GetPixel(screenDC, x, y);
            if (color != CLR_INVALID) {
                PixelData pixel;
                pixel.x = x;
                pixel.y = y;
                pixel.r = GetRValue(color);
                pixel.g = GetGValue(color);
                pixel.b = GetBValue(color);
                g_baselinePixels.push_back(pixel);
            }
        }
    }

    ReleaseDC(NULL, screenDC);
    g_pixelsLearned = !g_baselinePixels.empty();
}

void MonitorPixelChanges() {
    if (!g_pixelsLearned) return;

    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastUpdate = std::chrono::duration_cast<std::chrono::milliseconds>(now - g_lastPixelUpdate).count();
    if (timeSinceLastUpdate < g_config.monitoringInterval) {
        return;
    }
    g_lastPixelUpdate = now;

    HDC screenDC = GetDC(NULL);
    g_changedPixelCount = 0;

    for (const auto& baselinePixel : g_baselinePixels) {
        COLORREF currentColor = GetPixel(screenDC, baselinePixel.x, baselinePixel.y);
        if (currentColor != CLR_INVALID) {
            int currentR = GetRValue(currentColor);
            int currentG = GetGValue(currentColor);
            int currentB = GetBValue(currentColor);

            int rDiff = abs(currentR - baselinePixel.r);
            int gDiff = abs(currentG - baselinePixel.g);
            int bDiff = abs(currentB - baselinePixel.b);

            if (rDiff > g_config.pixelTolerance || gDiff > g_config.pixelTolerance || bDiff > g_config.pixelTolerance) {
                g_changedPixelCount++;
            }
        }
    }

    ReleaseDC(NULL, screenDC);
}

bool DetectPixelChange() {
    return g_changedPixelCount >= g_config.minChangedPixels;
}

void SimulateMouseClick() {
    INPUT input = {0};
    input.type = INPUT_MOUSE;
    input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
    SendInput(1, &input, sizeof(INPUT));

    std::this_thread::sleep_for(std::chrono::milliseconds(g_config.shootTime));

    input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
    SendInput(1, &input, sizeof(INPUT));
}

void UpdateTriggerbot() {
    if (g_config.enabled && !g_config.wasEnabled) {
        g_config.keyWasPressed = false;
    }

    g_config.wasEnabled = g_config.enabled;

    if (!g_config.enabled) {
        g_triggerbotActive = false;
        g_pixelChangeDetected = false;
        return;
    }

    bool activationPressed = (GetAsyncKeyState(g_config.activationKey) & 0x8000) != 0;

    if (g_config.captureOnKeyPress && activationPressed && !g_config.keyWasPressed) {
        CaptureBaselinePixels();
        g_config.keyWasPressed = true;
    }

    if (!activationPressed) {
        g_config.keyWasPressed = false;
    }

    if (activationPressed && g_pixelsLearned) {
        MonitorPixelChanges();
        g_pixelChangeDetected = DetectPixelChange();

        if (g_config.shootOnPixelChange && g_pixelChangeDetected) {
            auto now = std::chrono::steady_clock::now();
            auto timeSinceLastShot = std::chrono::duration_cast<std::chrono::milliseconds>(now - g_lastShot).count();

            if (timeSinceLastShot >= g_config.reactionDelay + g_config.shootTime + 100) {
                SimulateMouseClick();
                g_lastShot = now;
                g_triggerbotActive = true;
            }
        }
    } else {
        g_triggerbotActive = false;
        g_pixelChangeDetected = false;
    }
}

const char* GetKeyName(int vk) {
    switch (vk) {
        case VK_LBUTTON: return "Left Mouse";
        case VK_RBUTTON: return "Right Mouse";
        case VK_MBUTTON: return "Middle Mouse";
        case VK_XBUTTON1: return "Mouse 4";
        case VK_XBUTTON2: return "Mouse 5";
        case VK_SHIFT: return "Shift";
        case VK_CONTROL: return "Ctrl";
        case VK_MENU: return "Alt";
        case VK_SPACE: return "Space";
        default: return "Unknown";
    }
}

void ResetPixelData() {
    g_baselinePixels.clear();
    g_pixelsLearned = false;
    g_pixelChangeDetected = false;
    g_changedPixelCount = 0;
}

// DirectX helper functions
bool CreateDeviceD3D(HWND hWnd)
{
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };
    HRESULT res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    if (res == DXGI_ERROR_UNSUPPORTED)
        res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_WARP, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    if (res != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void CleanupDeviceD3D()
{
    CleanupRenderTarget();
    if (g_pSwapChain) { g_pSwapChain->Release(); g_pSwapChain = nullptr; }
    if (g_pd3dDeviceContext) { g_pd3dDeviceContext->Release(); g_pd3dDeviceContext = nullptr; }
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = nullptr; }
}

void CreateRenderTarget()
{
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget()
{
    if (g_mainRenderTargetView) { g_mainRenderTargetView->Release(); g_mainRenderTargetView = nullptr; }
}

extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg)
    {
    case WM_SIZE:
        if (wParam == SIZE_MINIMIZED)
            return 0;
        g_ResizeWidth = (UINT)LOWORD(lParam);
        g_ResizeHeight = (UINT)HIWORD(lParam);
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU)
            return 0;
        break;
    case WM_DESTROY:
        ::PostQuitMessage(0);
        return 0;
    }
    return ::DefWindowProcW(hWnd, msg, wParam, lParam);
}
